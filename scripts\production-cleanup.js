#!/usr/bin/env node

/**
 * Production Cleanup Script
 * Removes development-only files and debugging code for production deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

console.log('🧹 Starting production cleanup...\n');

// Files and directories to remove for production
const debugFilesToRemove = [
  'pages/admin/auth-debug.js',
  'pages/admin/diagnostics',
  'pages/debug',
  'pages/debug-auth.js',
  'test-*.js',
  'test-*.html',
  'debug-*.js',
  'debug-*.html',
  'browser-debug.js',
  'quick-error-check.js',
  'node-error-check.js',
  'node-error-check.cjs',
  'public/console-monitor.js',
  'public/test-*.html',
  'public/test-*.js'
];

// Patterns to clean from files
const cleanupPatterns = [
  {
    pattern: /console\.log\([^)]*\);?\s*$/gm,
    replacement: '// Debug log removed for production',
    description: 'Console.log statements'
  },
  {
    pattern: /console\.debug\([^)]*\);?\s*$/gm,
    replacement: '// Debug statement removed for production',
    description: 'Console.debug statements'
  },
  {
    pattern: /\/\/ DEBUG:.*$/gm,
    replacement: '// Debug comment removed for production',
    description: 'Debug comments'
  }
];

/**
 * Remove debug files and directories
 */
function removeDebugFiles() {
  console.log('📁 Removing debug files and directories...\n');
  
  debugFilesToRemove.forEach(item => {
    const fullPath = path.join(rootDir, item);
    
    try {
      if (fs.existsSync(fullPath)) {
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory()) {
          fs.rmSync(fullPath, { recursive: true, force: true });
          console.log(`✅ Removed directory: ${item}`);
        } else {
          fs.unlinkSync(fullPath);
          console.log(`✅ Removed file: ${item}`);
        }
      } else {
        console.log(`ℹ️  Not found (already clean): ${item}`);
      }
    } catch (error) {
      console.log(`❌ Error removing ${item}: ${error.message}`);
    }
  });
}

/**
 * Clean up specific patterns in source files
 */
function cleanupSourceFiles() {
  console.log('\n🔧 Cleaning up source files...\n');
  
  const sourceExtensions = ['.js', '.jsx', '.ts', '.tsx'];
  const excludeDirs = ['node_modules', '.next', '.git', 'scripts'];
  
  function processDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const relativePath = path.relative(rootDir, fullPath);
      
      // Skip excluded directories
      if (excludeDirs.some(excluded => relativePath.startsWith(excluded))) {
        return;
      }
      
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        processDirectory(fullPath);
      } else if (sourceExtensions.includes(path.extname(item))) {
        cleanupFile(fullPath, relativePath);
      }
    });
  }
  
  function cleanupFile(filePath, relativePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      cleanupPatterns.forEach(({ pattern, replacement, description }) => {
        const matches = content.match(pattern);
        if (matches && matches.length > 0) {
          content = content.replace(pattern, replacement);
          modified = true;
          console.log(`🔧 Cleaned ${matches.length} ${description} in ${relativePath}`);
        }
      });
      
      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Updated: ${relativePath}`);
      }
    } catch (error) {
      console.log(`❌ Error processing ${relativePath}: ${error.message}`);
    }
  }
  
  processDirectory(rootDir);
}

/**
 * Verify production configuration
 */
function verifyProductionConfig() {
  console.log('\n🔍 Verifying production configuration...\n');
  
  const envLocalPath = path.join(rootDir, '.env.local');
  
  if (fs.existsSync(envLocalPath)) {
    const envContent = fs.readFileSync(envLocalPath, 'utf8');
    
    const productionChecks = [
      { pattern: /NEXT_PUBLIC_DEV_MODE=false/, name: 'Dev mode disabled' },
      { pattern: /NEXT_PUBLIC_DEBUG_AUTH=false/, name: 'Debug auth disabled' },
      { pattern: /ENABLE_AUTH_BYPASS=false/, name: 'Auth bypass disabled' },
      { pattern: /SQUARE_ENVIRONMENT=production/, name: 'Square production mode' }
    ];
    
    productionChecks.forEach(({ pattern, name }) => {
      if (pattern.test(envContent)) {
        console.log(`✅ ${name}`);
      } else {
        console.log(`❌ ${name} - NEEDS ATTENTION`);
      }
    });
  } else {
    console.log('❌ .env.local file not found');
  }
}

/**
 * Main execution
 */
function main() {
  try {
    removeDebugFiles();
    cleanupSourceFiles();
    verifyProductionConfig();
    
    console.log('\n🎉 Production cleanup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Review the changes made');
    console.log('2. Test the application in development mode');
    console.log('3. Build and test in production mode');
    console.log('4. Deploy to production');
    
  } catch (error) {
    console.error('❌ Production cleanup failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
