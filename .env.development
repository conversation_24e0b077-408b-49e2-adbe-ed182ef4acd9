# Development Environment Configuration
# This file is used when NODE_ENV=development or when running npm run dev

# Supabase Configuration (same as production)
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY

# Site Configuration - Development URLs (flexible for any Vercel deployment)
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_ADMIN_URL=http://localhost:3000/admin

# OneSignal Configuration (same as production)
NEXT_PUBLIC_ONESIGNAL_APP_ID=************************************
ONESIGNAL_API_KEY=
ONESIGNAL_REST_API_KEY=nivzmsejbeoiehtwsovd4sjyq

# Email Configuration (same as production)
GMAIL_SMTP_HOST=smtp.gmail.com
GMAIL_SMTP_PORT=587
GMAIL_SMTP_SECURE=false
GMAIL_SMTP_USER=<EMAIL>
GMAIL_SMTP_APP_PASSWORD=jjmfjcfqqrzgsogy
GMAIL_FROM_NAME=OceanSoulSparkles
GMAIL_FROM_EMAIL=<EMAIL>

# Google Configuration (same as production)
GOOGLE_CLIENT_ID=429450887908-bdaro6svl47787ec21bi11251eoolj9n.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Development Security Configuration - More permissive
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_DEBUG_AUTH=true
ENABLE_AUTH_BYPASS=true
NEXT_PUBLIC_ENABLE_AUTH_BYPASS=true
FORCE_EMAIL_IN_DEV=true

# Square Configuration - Sandbox for development
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-zmeKEI4JNUCFsS4wkL7jjQ
NEXT_PUBLIC_SQUARE_LOCATION_ID=L2DSKTPV3D3YT
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_ENVIRONMENT=sandbox

# Security Headers - Disabled for development flexibility
NEXT_PUBLIC_ENABLE_SECURITY_HEADERS=false
NEXT_PUBLIC_DISABLE_CONSOLE_LOGS=false
