#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Square Refund Configuration Test
 * 
 * This script tests the Square API configuration for refund processing
 * Run with: node scripts/test-square-refund-config.js
 */

require('dotenv').config({ path: '.env.local' });

const REQUIRED_ENV_VARS = [
  'NEXT_PUBLIC_SQUARE_APPLICATION_ID',
  'NEXT_PUBLIC_SQUARE_LOCATION_ID', 
  'SQUARE_ACCESS_TOKEN',
  'SQUARE_ENVIRONMENT'
];

const SQUARE_API_VERSION = '2023-10-18';

async function testSquareConfiguration() {
  console.log('🔧 Ocean Soul Sparkles Square Refund Configuration Test\n');

  // Check environment variables
  console.log('📋 Checking Environment Variables:');
  let configValid = true;

  for (const envVar of REQUIRED_ENV_VARS) {
    const value = process.env[envVar];
    if (value) {
      console.log(`✅ ${envVar}: ${envVar.includes('TOKEN') ? '***' + value.slice(-4) : value}`);
    } else {
      console.log(`❌ ${envVar}: Missing`);
      configValid = false;
    }
  }

  if (!configValid) {
    console.log('\n❌ Configuration incomplete. Please set all required environment variables.');
    process.exit(1);
  }

  // Test Square API connectivity
  console.log('\n🌐 Testing Square API Connectivity:');
  
  const squareEnvironment = process.env.SQUARE_ENVIRONMENT;
  const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN;
  const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;

  const apiUrl = squareEnvironment === 'production' 
    ? 'https://connect.squareup.com'
    : 'https://connect.squareupsandbox.com';

  console.log(`🔗 Environment: ${squareEnvironment}`);
  console.log(`🔗 API URL: ${apiUrl}`);

  try {
    // Test 1: Get location details
    console.log('\n🧪 Test 1: Fetching location details...');
    const locationResponse = await fetch(`${apiUrl}/v2/locations/${squareLocationId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${squareAccessToken}`,
        'Square-Version': SQUARE_API_VERSION,
        'Content-Type': 'application/json'
      }
    });

    if (locationResponse.ok) {
      const locationData = await locationResponse.json();
      console.log(`✅ Location found: ${locationData.location?.name || 'Unknown'}`);
      console.log(`   Address: ${locationData.location?.address?.locality || 'N/A'}`);
      console.log(`   Status: ${locationData.location?.status || 'N/A'}`);
    } else {
      const errorData = await locationResponse.json();
      console.log(`❌ Location API error: ${errorData.errors?.[0]?.detail || 'Unknown error'}`);
      return false;
    }

    // Test 2: List recent payments (to verify refund capability)
    console.log('\n🧪 Test 2: Checking payment access...');
    const paymentsResponse = await fetch(`${apiUrl}/v2/payments`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${squareAccessToken}`,
        'Square-Version': SQUARE_API_VERSION,
        'Content-Type': 'application/json'
      }
    });

    if (paymentsResponse.ok) {
      const paymentsData = await paymentsResponse.json();
      const paymentCount = paymentsData.payments?.length || 0;
      console.log(`✅ Payment API accessible (${paymentCount} recent payments found)`);
    } else {
      const errorData = await paymentsResponse.json();
      console.log(`❌ Payments API error: ${errorData.errors?.[0]?.detail || 'Unknown error'}`);
    }

    // Test 3: Check refunds API access
    console.log('\n🧪 Test 3: Checking refunds API access...');
    const refundsResponse = await fetch(`${apiUrl}/v2/refunds`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${squareAccessToken}`,
        'Square-Version': SQUARE_API_VERSION,
        'Content-Type': 'application/json'
      }
    });

    if (refundsResponse.ok) {
      const refundsData = await refundsResponse.json();
      const refundCount = refundsData.refunds?.length || 0;
      console.log(`✅ Refunds API accessible (${refundCount} recent refunds found)`);
    } else {
      const errorData = await refundsResponse.json();
      console.log(`❌ Refunds API error: ${errorData.errors?.[0]?.detail || 'Unknown error'}`);
    }

    console.log('\n🎉 Square API configuration test completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('1. Navigate to http://localhost:3000/admin/payments');
    console.log('2. Look for the test payment (Amount: $150.00)');
    console.log('3. Click the "Refund" button to test refund processing');
    console.log('4. Process a partial refund (e.g., $75.00) to test functionality');

    return true;

  } catch (error) {
    console.log(`❌ Network error: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check internet connectivity');
    console.log('2. Verify Square API credentials');
    console.log('3. Ensure correct environment (sandbox vs production)');
    return false;
  }
}

// Test Ocean Soul Sparkles database connection
async function testDatabaseConnection() {
  console.log('\n🗄️  Testing Database Connection:');
  
  try {
    // Simple test to verify we can connect to the database
    const testQuery = `
      SELECT COUNT(*) as payment_count 
      FROM public.payments 
      WHERE payment_status = 'completed' 
      AND payment_method LIKE '%square%'
    `;

    console.log('✅ Database connection test would go here');
    console.log('   (Requires Supabase client setup)');
    
  } catch (error) {
    console.log(`❌ Database connection error: ${error.message}`);
  }
}

// Main execution
async function main() {
  const success = await testSquareConfiguration();
  await testDatabaseConnection();
  
  if (success) {
    console.log('\n✅ All tests passed! Square refund system is ready for use.');
    process.exit(0);
  } else {
    console.log('\n❌ Some tests failed. Please check configuration and try again.');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(error => {
    console.error('Test script error:', error);
    process.exit(1);
  });
}

module.exports = { testSquareConfiguration };
