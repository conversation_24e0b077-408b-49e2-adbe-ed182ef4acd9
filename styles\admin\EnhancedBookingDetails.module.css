.enhancedDetails {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  border-bottom: 1px solid #e1e5e9;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.bookingTitle h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.bookingMeta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.status {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.confirmed {
  background: #d4edda;
  color: #155724;
}

.status.pending {
  background: #fff3cd;
  color: #856404;
}

.status.canceled {
  background: #f8d7da;
  color: #721c24;
}

.status.completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status.inProgress {
  background: #e2e3e5;
  color: #383d41;
}

.status.noShow {
  background: #f5c6cb;
  color: #721c24;
}

.service {
  background: #3788d8;
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.priority {
  background: #e74c3c;
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.headerActions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.editButton {
  background: #3788d8;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton:hover {
  background: #2c6bb8;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 8px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.closeButton:hover {
  background: #f5f5f5;
}

.quickActions {
  display: flex;
  gap: 8px;
  padding: 16px 24px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  flex-wrap: wrap;
}

.quickAction {
  background: white;
  border: 1px solid #ddd;
  color: #2c3e50;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.quickAction:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #3788d8;
}

.quickAction:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tabNavigation {
  display: flex;
  border-bottom: 1px solid #e1e5e9;
  background: white;
}

.tab {
  background: none;
  border: none;
  padding: 16px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  justify-content: center;
}

.tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.tab.active {
  color: #3788d8;
  border-bottom-color: #3788d8;
  background: #f8f9fa;
}

.tabIcon {
  font-size: 16px;
}

.tabContent {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Details Tab Styles */
.detailsTab {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.infoSection {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.infoSection h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #3788d8;
  padding-bottom: 8px;
}

.infoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.infoRow:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.infoLabel {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.infoValue {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
  text-align: right;
}

.serviceInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.serviceColor {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.serviceName {
  font-weight: 600;
  color: #2c3e50;
}

.servicePrice {
  font-size: 14px;
  color: #666;
}

.statusManagement {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.statusManagement h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.notesSection {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.notesSection h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.notesContent {
  margin: 0;
  line-height: 1.6;
  color: #2c3e50;
  white-space: pre-wrap;
}

/* Customer Tab Styles */
.customerTab {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.customerInfo {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.customerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.customerHeader h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #3788d8;
  padding-bottom: 8px;
}

.viewCustomerLink {
  color: #3788d8;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  border: 1px solid #3788d8;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.viewCustomerLink:hover {
  background: #3788d8;
  color: white;
  text-decoration: none;
}

.loadingCustomer {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.customerDetails {
  /* Inherits from existing styles */
}

.customerTier {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.customerTier.bronze {
  background: #cd7f32;
  color: white;
}

.customerTier.silver {
  background: #c0c0c0;
  color: #333;
}

.customerTier.gold {
  background: #ffd700;
  color: #333;
}

.customerTier.platinum {
  background: #e5e4e2;
  color: #333;
}

.customerStatus {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.customerStatus.active {
  background: #d4edda;
  color: #155724;
}

.customerStatus.inactive {
  background: #f8d7da;
  color: #721c24;
}

.customerStatus.deactivated {
  background: #e2e3e5;
  color: #383d41;
}

.customerStats {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.customerStats h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #3788d8;
  padding-bottom: 8px;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.statItem {
  text-align: center;
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.statValue {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #3788d8;
  margin-bottom: 4px;
}

.statLabel {
  display: block;
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.customerInfo a {
  color: #3788d8;
  text-decoration: none;
}

.customerInfo a:hover {
  text-decoration: underline;
}

.customerHistory {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.customerHistory h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #3788d8;
  padding-bottom: 8px;
}

.historyList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.historyItem {
  display: grid;
  grid-template-columns: 1fr 1fr auto auto;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
  align-items: center;
}

.historyDate {
  font-size: 13px;
  color: #666;
}

.historyService {
  font-weight: 500;
  color: #2c3e50;
}

.historyRevenue {
  font-weight: 600;
  color: #28a745;
  font-size: 14px;
  text-align: right;
}

.historyStatus {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.moreHistory {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 12px;
}

.viewAllLink {
  color: #3788d8;
  text-decoration: none;
  font-weight: 500;
}

.viewAllLink:hover {
  text-decoration: underline;
}

.noHistory {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

/* Communications Tab Styles */
.communicationsTab {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.communicationsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.communicationsHeader h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.refreshButton {
  background: #3788d8;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refreshButton:hover {
  background: #2c6bb8;
}

.communicationsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.noCommunications {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
}

.communicationItem {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
}

.commHeader {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.commType {
  background: #3788d8;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.commDirection {
  background: #6c757d;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.commDate {
  color: #666;
  font-size: 12px;
  margin-left: auto;
}

.commSubject {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.commContent {
  color: #2c3e50;
  line-height: 1.5;
  margin-bottom: 8px;
}

.commStatus {
  font-size: 12px;
  color: #666;
}

.errorMessage {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin: 16px 24px;
  border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhancedDetails {
    max-width: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .headerActions {
    justify-content: space-between;
  }

  .quickActions {
    justify-content: center;
  }

  .tabNavigation {
    flex-wrap: wrap;
  }

  .tab {
    flex: 1;
    min-width: 120px;
  }

  .infoGrid {
    grid-template-columns: 1fr;
  }

  .historyItem {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .bookingMeta {
    flex-direction: column;
    gap: 8px;
  }

  .quickActions {
    flex-direction: column;
  }

  .quickAction {
    width: 100%;
    justify-content: center;
  }

  .tab {
    padding: 12px 8px;
    font-size: 12px;
  }

  .tabContent {
    padding: 16px;
  }
}
