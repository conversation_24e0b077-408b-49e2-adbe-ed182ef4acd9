import { supabaseAdmin } from '@/lib/supabase';
import authTokenManager from '@/lib/auth-token-manager';

/**
 * Individual Payment Management API
 *
 * GET /api/admin/payments/{id}
 * PUT /api/admin/payments/{id}
 * DELETE /api/admin/payments/{id}
 *
 * Manages individual payment operations
 * Used by PaymentDetailPage component for viewing and managing payment records
 */
export default async function handler(req, res) {
  // Generate request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Individual payment request started`);

  try {
    // Verify authentication
    const authResult = await authTokenManager.verifyToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user has admin or staff role
    const userRole = authResult.user?.role;
    if (!userRole || !['dev', 'admin', 'artist', 'braider'].includes(userRole)) {
      console.log(`[${requestId}] Insufficient permissions. User role:`, userRole);
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    // Extract payment ID from URL
    const { id: paymentId } = req.query;

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    console.log(`[${requestId}] Processing ${req.method} request for payment: ${paymentId}`);

    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getPayment(paymentId, requestId, res);
      case 'PUT':
        return await updatePayment(paymentId, req.body, authResult.user, requestId, res);
      case 'DELETE':
        // Only admins and devs can delete payments
        if (!['dev', 'admin'].includes(userRole)) {
          return res.status(403).json({ error: 'Only admins can delete payments' });
        }
        return await deletePayment(paymentId, authResult.user, requestId, res);
      default:
        console.log(`[${requestId}] Method ${req.method} not allowed`);
        return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error(`[${requestId}] Error in individual payment API:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while managing payment'
    });
  }
}

// Get a single payment with full details
async function getPayment(paymentId, requestId, res) {
  console.log(`[${requestId}] Fetching payment: ${paymentId}`);

  const { data: payment, error } = await supabaseAdmin
    .from('payments')
    .select(`
      id,
      order_id,
      booking_id,
      amount,
      currency,
      payment_method,
      payment_status,
      transaction_id,
      payment_date,
      notes,
      pos_session_id,
      square_payment_id,
      receipt_url,
      created_at,
      updated_at,
      bookings:booking_id (
        id,
        customer_id,
        service_id,
        start_time,
        end_time,
        status,
        location,
        notes,
        booking_reference,
        booking_source,
        tier_name,
        tier_price,
        customers:customer_id (
          id,
          name,
          email,
          phone,
          address,
          city,
          state
        ),
        services:service_id (
          id,
          name,
          color,
          price,
          duration,
          category
        )
      ),
      orders:order_id (
        id,
        customer_id,
        order_date,
        status,
        payment_status,
        total,
        customers:customer_id (
          id,
          name,
          email,
          phone,
          address,
          city,
          state
        )
      )
    `)
    .eq('id', paymentId)
    .single();

  if (error) {
    console.error(`[${requestId}] Error fetching payment:`, error);
    
    if (error.code === 'PGRST116') {
      return res.status(404).json({ 
        error: 'Payment not found',
        requestId 
      });
    }
    
    throw error;
  }

  if (!payment) {
    console.log(`[${requestId}] Payment not found: ${paymentId}`);
    return res.status(404).json({ 
      error: 'Payment not found',
      requestId 
    });
  }

  // Get refund history for this payment
  const { data: refunds, error: refundsError } = await supabaseAdmin
    .from('refunds')
    .select(`
      id,
      refund_amount,
      currency,
      refund_reason,
      refund_notes,
      refund_method,
      refund_status,
      square_refund_id,
      processed_at,
      completed_at,
      created_at
    `)
    .eq('payment_id', paymentId)
    .order('created_at', { ascending: false });

  if (refundsError) {
    console.error(`[${requestId}] Error fetching refunds:`, refundsError);
    // Don't fail the request if refunds can't be fetched
  }

  console.log(`[${requestId}] Successfully fetched payment with ${refunds?.length || 0} refunds`);

  return res.status(200).json({
    payment: {
      ...payment,
      refunds: refunds || []
    },
    requestId
  });
}

// Update a payment record
async function updatePayment(paymentId, updateData, user, requestId, res) {
  console.log(`[${requestId}] Updating payment: ${paymentId}`);

  // Validate required fields
  if (!paymentId) {
    return res.status(400).json({ error: 'Payment ID is required' });
  }

  // Remove fields that shouldn't be updated directly
  const { id, created_at, refunds, bookings, orders, ...allowedUpdates } = updateData;

  // Add updated_at timestamp
  allowedUpdates.updated_at = new Date().toISOString();

  // Update the payment
  const { data: updatedPayment, error: updateError } = await supabaseAdmin
    .from('payments')
    .update(allowedUpdates)
    .eq('id', paymentId)
    .select(`
      id,
      order_id,
      booking_id,
      amount,
      currency,
      payment_method,
      payment_status,
      transaction_id,
      payment_date,
      notes,
      pos_session_id,
      square_payment_id,
      receipt_url,
      created_at,
      updated_at,
      bookings:booking_id (
        id,
        customer_id,
        service_id,
        start_time,
        end_time,
        status,
        location,
        notes,
        booking_reference,
        booking_source,
        tier_name,
        tier_price,
        customers:customer_id (
          id,
          name,
          email,
          phone,
          address,
          city,
          state
        ),
        services:service_id (
          id,
          name,
          color,
          price,
          duration,
          category
        )
      ),
      orders:order_id (
        id,
        customer_id,
        order_date,
        status,
        payment_status,
        total,
        customers:customer_id (
          id,
          name,
          email,
          phone,
          address,
          city,
          state
        )
      )
    `)
    .single();

  if (updateError) {
    console.error(`[${requestId}] Error updating payment:`, updateError);
    throw updateError;
  }

  console.log(`[${requestId}] Successfully updated payment`);

  return res.status(200).json({
    payment: updatedPayment,
    requestId
  });
}

// Delete a payment record (Admin/DEV only)
async function deletePayment(paymentId, user, requestId, res) {
  console.log(`[${requestId}] Deleting payment: ${paymentId}`);

  // Check if payment has any refunds
  const { data: refunds, error: refundsError } = await supabaseAdmin
    .from('refunds')
    .select('id')
    .eq('payment_id', paymentId);

  if (refundsError) {
    console.error(`[${requestId}] Error checking refunds:`, refundsError);
    throw refundsError;
  }

  if (refunds && refunds.length > 0) {
    return res.status(400).json({
      error: 'Cannot delete payment with existing refunds',
      requestId
    });
  }

  // Delete the payment
  const { error: deleteError } = await supabaseAdmin
    .from('payments')
    .delete()
    .eq('id', paymentId);

  if (deleteError) {
    console.error(`[${requestId}] Error deleting payment:`, deleteError);
    throw deleteError;
  }

  console.log(`[${requestId}] Successfully deleted payment`);

  return res.status(200).json({
    message: 'Payment deleted successfully',
    requestId
  });
}
