# Ocean Soul Sparkles Square Refund Processing Guide

## Overview

This comprehensive guide covers how to process Square API refunds through the Ocean Soul Sparkles admin interface, including configuration, testing, and troubleshooting.

## 1. Step-by-Step Instructions for Processing Square Refunds

### Accessing the Refund Functionality

#### Step 1: Navigate to Payment Management
1. Log into the Ocean Soul Sparkles admin panel
2. Navigate to **Payment Management** from the main admin menu
3. You'll see the payments list with search and filter capabilities

#### Step 2: Identify Eligible Payments
Look for payments that meet these criteria:
- **Status**: `completed` (only completed payments can be refunded)
- **Payment Method**: Contains "square" (e.g., `square_terminal`, `square_reader`, `square_card`)
- **Refund Button**: Visible only to Admin and DEV role users

**Visual Indicators:**
- ✅ **Green "Refund" button** appears in the Actions column for eligible payments
- 🔒 **No refund button** for non-Square payments or non-Admin users
- ⚠️ **Disabled state** for already refunded payments

#### Step 3: Initiate Refund Process
1. Click the **"Refund"** button for the desired payment
2. The RefundModal will open with payment details displayed

### Using the RefundModal Interface

#### Payment Details Section
The modal displays:
- **Payment ID**: First 8 characters + "..."
- **Original Amount**: Full payment amount in AUD
- **Payment Method**: e.g., "square_terminal"
- **Transaction ID**: Square transaction identifier
- **Total Refunded**: Amount already refunded (if any)
- **Remaining Amount**: Available for refund

#### Refund Form Fields

**1. Refund Amount** (Required)
- Enter the amount to refund (up to remaining balance)
- Supports partial and full refunds
- Validation prevents over-refunding

**2. Refund Reason** (Required)
- **Customer Request**: Customer-initiated refund
- **Service Issue**: Problem with service delivery
- **Billing Error**: Incorrect charge or billing mistake
- **Other**: Custom circumstances

**3. Refund Method** (Auto-selected for Square payments)
- **Square API**: Automatically selected for Square payments
- **Cash**: For manual cash refunds
- **Manual**: For custom processing

**4. Refund Notes** (Optional)
- Add internal notes about the refund
- Visible in refund history for audit purposes

#### Step 4: Process the Refund
1. Fill in all required fields
2. Click **"Process Refund"** button
3. Confirm in the confirmation dialog
4. Wait for processing (usually 2-5 seconds for Square API)

### Expected Results
- **Success**: Green confirmation message with refund ID
- **Payment Status Update**: Automatically changes to `partially_refunded` or `refunded`
- **Refund History**: New entry appears in the refund history section
- **Square Dashboard**: Refund appears in Square seller dashboard

## 2. Square API Configuration Requirements

### Environment Variables

Ensure these variables are set in your `.env.local` file:

```env
# Square Configuration
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-zmeKEI4JNUCFsS4wkL7jjQ
NEXT_PUBLIC_SQUARE_LOCATION_ID=L2DSKTPV3D3YT
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_ENVIRONMENT=sandbox  # Change to 'production' when ready
```

### Sandbox vs Production Configuration

#### Sandbox Environment (Testing)
```env
SQUARE_ENVIRONMENT=sandbox
SQUARE_ACCESS_TOKEN=****************************************************************
```
- **API URL**: `https://connect.squareupsandbox.com`
- **Purpose**: Testing refunds without real money
- **Refunds**: Processed instantly in sandbox
- **Dashboard**: View at `https://squareupsandbox.com/dashboard`

#### Production Environment (Live)
```env
SQUARE_ENVIRONMENT=production
SQUARE_ACCESS_TOKEN=your_production_access_token
```
- **API URL**: `https://connect.squareup.com`
- **Purpose**: Real refunds with actual money
- **Refunds**: May take 1-3 business days to appear in customer accounts
- **Dashboard**: View at `https://squareup.com/dashboard`

### Verifying Square API Credentials

#### Method 1: Admin Interface Check
1. Navigate to **Admin → Settings → Payment Configuration**
2. Look for Square API status indicators
3. Green checkmark indicates valid credentials

#### Method 2: API Test Call
```bash
curl -X GET \
  https://connect.squareupsandbox.com/v2/locations \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Square-Version: 2023-10-18"
```

#### Method 3: Browser Console Test
1. Open browser developer tools
2. Navigate to Payment Management
3. Check console for Square API initialization messages

## 3. Testing Square Refund Functionality

### Using the Test Payment

A test payment has been created for demonstration:
- **Payment ID**: `d2b0927a-f7dd-49cf-96d4-7b04c17c666d`
- **Amount**: $150.00 AUD
- **Status**: completed
- **Method**: square_terminal
- **Transaction ID**: test_payment_refund_demo_001

### Test Scenarios

#### Test 1: Partial Refund
1. Navigate to Payment Management
2. Find the test payment (search by amount: 150.00)
3. Click "Refund" button
4. Enter refund amount: `75.00`
5. Select reason: "Customer Request"
6. Add notes: "Testing partial refund functionality"
7. Process refund

**Expected Results:**
- Payment status changes to `partially_refunded`
- Remaining amount shows `$75.00`
- Refund appears in history

#### Test 2: Full Refund (Remaining Amount)
1. Process another refund for the remaining `$75.00`
2. Payment status should change to `refunded`
3. No more refund button should appear

### Monitoring Refund Process

#### Real-Time Status Updates
Watch for these status changes:
1. **Initial**: `pending` (while processing with Square)
2. **Success**: `completed` (Square confirms refund)
3. **Failure**: `failed` (Square rejects refund)

#### API Response Monitoring
Check browser console for:
```javascript
[abc123] Processing Square refund: {idempotency_key: "...", amount_money: {...}}
[abc123] Refund created successfully: refund-uuid
```

#### Square Dashboard Verification
1. Log into Square Dashboard (sandbox or production)
2. Navigate to **Transactions → Refunds**
3. Verify refund appears with correct amount and timestamp

## 4. Troubleshooting Common Issues

### Square API Refund Failures

#### Error: "Square API credentials not configured"
**Cause**: Missing or invalid `SQUARE_ACCESS_TOKEN`
**Solution**: 
1. Verify environment variable is set
2. Check token format (starts with "EAAA" for sandbox)
3. Restart development server after changes

#### Error: "Payment not found in Square"
**Cause**: Transaction ID doesn't exist in Square system
**Solution**:
1. Verify the payment was actually processed through Square
2. Check if using correct environment (sandbox vs production)
3. Ensure transaction_id matches Square payment ID

#### Error: "Refund amount exceeds available balance"
**Cause**: Trying to refund more than remaining amount
**Solution**:
1. Check existing refunds for this payment
2. Calculate remaining refundable amount
3. Process partial refund if needed

### Partial vs Full Refunds

#### Partial Refunds
- **Use Case**: Customer keeps some services, refunds others
- **Process**: Enter amount less than total payment
- **Result**: Payment status becomes `partially_refunded`
- **Limitation**: Can process multiple partial refunds up to total amount

#### Full Refunds
- **Use Case**: Complete cancellation of services
- **Process**: Enter full remaining amount
- **Result**: Payment status becomes `refunded`
- **Note**: No further refunds possible after full refund

### Common Error Messages

#### "Method not allowed"
- **Meaning**: API endpoint called with wrong HTTP method
- **Solution**: Ensure using POST for refund processing

#### "Unauthorized"
- **Meaning**: User lacks Admin/DEV role permissions
- **Solution**: Verify user role in database

#### "Refund not allowed"
- **Meaning**: Payment not eligible for refund
- **Possible Causes**:
  - Payment status not "completed"
  - Already fully refunded
  - Invalid payment ID

#### "Square refund failed"
- **Meaning**: Square API rejected the refund request
- **Common Causes**:
  - Invalid transaction ID
  - Refund amount exceeds available balance
  - Network connectivity issues
  - Square API rate limiting

### Debug Mode

Enable detailed logging by setting:
```env
NODE_ENV=development
```

This provides:
- Detailed API request/response logs
- Square SDK initialization messages
- Refund processing step-by-step logs

### Emergency Procedures

#### If Square API is Down
1. Use "Manual" refund method
2. Process refund manually in Square Dashboard
3. Update Ocean Soul Sparkles refund record manually
4. Add notes explaining manual processing

#### If Refund Appears Stuck
1. Check Square Dashboard for actual refund status
2. Update refund record in database if needed
3. Contact Square support for payment disputes

## 5. Best Practices

### Before Processing Refunds
- ✅ Verify customer identity and refund request
- ✅ Check refund policy compliance
- ✅ Document reason for refund
- ✅ Confirm refund amount with customer

### During Processing
- ✅ Use appropriate refund reason codes
- ✅ Add detailed notes for audit trail
- ✅ Verify refund amount before confirming
- ✅ Monitor for successful completion

### After Processing
- ✅ Confirm refund appears in Square Dashboard
- ✅ Notify customer of refund processing
- ✅ Update any related booking/order records
- ✅ Document in customer service records

## 6. Quick Demo Instructions

For a hands-on demonstration, follow these steps:

### Immediate Testing
1. **Open Admin Panel**: Navigate to `http://localhost:3000/admin/payments`
2. **Find Test Payment**: Look for $150.00 payment with ID `d2b0927a-f7dd-49cf-96d4-7b04c17c666d`
3. **Click Refund Button**: Red "Refund" button in Actions column
4. **Process Partial Refund**:
   - Amount: `75.00`
   - Reason: "Customer Request"
   - Notes: "Testing refund functionality"
5. **Verify Results**: Payment status changes to "partially_refunded"

### Expected Behavior
- ✅ RefundModal opens with payment details
- ✅ Form validates refund amount (max $150.00)
- ✅ Square API processes refund in 2-5 seconds
- ✅ Payment status updates automatically
- ✅ Refund appears in history section

### Visual Indicators
- 🟢 **Green status badge**: "completed" payments
- 🟡 **Yellow status badge**: "partially_refunded" payments
- 🔴 **Red refund button**: Available for eligible payments
- 🔒 **No refund button**: For non-Admin users or ineligible payments

## 7. Configuration Checklist

### Environment Variables ✅
```env
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_ENVIRONMENT=sandbox
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-zmeKEI4JNUCFsS4wkL7jjQ
NEXT_PUBLIC_SQUARE_LOCATION_ID=L2DSKTPV3D3YT
```

### Database Setup ✅
- Refunds table created with proper constraints
- RLS policies enforcing Admin/DEV access only
- Helper functions for refund validation
- Triggers for automatic payment status updates

### API Endpoints ✅
- `POST /api/admin/payments/[id]/refund` - Process refunds
- `GET /api/admin/payments/[id]/refunds` - Get refund history
- Role-based authentication implemented
- Square API integration functional

### UI Components ✅
- RefundModal with comprehensive form
- PaymentList integration with refund buttons
- Responsive design for mobile admin access
- Confirmation dialogs for security

## 8. Support Resources

### Square Documentation
- [Square Refunds API](https://developer.squareup.com/reference/square/refunds-api)
- [Square Dashboard Help](https://squareup.com/help)
- [Square Sandbox Testing](https://developer.squareup.com/docs/testing/sandbox)

### Ocean Soul Sparkles Internal
- **Database Schema**: See `admin-docs/refund-system.md`
- **Demo Guide**: See `admin-docs/square-refund-demo-steps.md`
- **API Documentation**: See refund API endpoints
- **Troubleshooting**: Check application logs and console output

### Emergency Contacts
- **Technical Issues**: Check browser console and server logs
- **Square API Issues**: Square Developer Support
- **Database Issues**: Supabase dashboard and documentation
- **Business Logic**: Review refund policies and procedures
