.paymentDetails {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  overflow-x: auto;
}

.tabButton {
  padding: 15px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tabButton:hover {
  background: #e9ecef;
  color: #495057;
}

.tabButton.active {
  background: white;
  color: #6e8efb;
  border-bottom-color: #6e8efb;
}

/* Tab Content */
.tabContent {
  padding: 30px;
}

.section {
  margin-bottom: 30px;
}

.section:last-child {
  margin-bottom: 0;
}

.section h3 {
  margin: 0 0 20px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 2px solid #f1f3f4;
}

/* Info Grid Layout */
.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.infoItem label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoItem span {
  font-size: 1rem;
  color: #333;
  word-break: break-word;
}

/* Special Styling for Specific Fields */
.transactionId,
.externalId,
.sessionId {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 0.9rem;
}

.amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #28a745;
}

/* Status Badges */
.statusBadge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusCompleted {
  background: #d4edda;
  color: #155724;
}

.statusPending {
  background: #fff3cd;
  color: #856404;
}

.statusFailed {
  background: #f8d7da;
  color: #721c24;
}

.statusRefunded {
  background: #d1ecf1;
  color: #0c5460;
}

.statusDefault {
  background: #e2e3e5;
  color: #383d41;
}

/* Notes Section */
.notes {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #6e8efb;
  font-style: italic;
  color: #666;
  line-height: 1.6;
}

/* Receipt Link */
.receiptLink {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #6e8efb;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.receiptLink:hover {
  background: #5a7cfa;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(110, 142, 251, 0.3);
}

.receiptLink::before {
  content: '📄';
  font-size: 1.1rem;
}

/* Customer Tab */
.customerTab {
  /* Inherits from tabContent */
}

.customerInfo {
  /* Inherits from section */
}

.customerHeader {
  margin-bottom: 20px;
}

.customerHeader h4 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.customerHeader h4 a {
  color: #6e8efb;
  text-decoration: none;
  transition: color 0.2s ease;
}

.customerHeader h4 a:hover {
  color: #5a7cfa;
  text-decoration: underline;
}

/* Service Tab */
.serviceTab {
  /* Inherits from tabContent */
}

.serviceInfo {
  /* Inherits from section */
}

.serviceHeader {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.serviceHeader h4 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.serviceCategory {
  background: #e9ecef;
  color: #495057;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Refunds Tab */
.refundsTab {
  /* Inherits from tabContent */
}

.refundsList {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.refundItem {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.refundItem:hover {
  border-color: #6e8efb;
  box-shadow: 0 2px 8px rgba(110, 142, 251, 0.1);
}

.refundHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.refundAmount {
  font-size: 1.25rem;
  font-weight: 700;
  color: #dc3545;
}

.refundDetails {
  color: #666;
  line-height: 1.6;
}

.refundDetails p {
  margin: 5px 0;
}

.refundDetails strong {
  color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tabNavigation {
    flex-wrap: wrap;
  }
  
  .tabButton {
    flex: 1;
    min-width: 120px;
    padding: 12px 15px;
    font-size: 0.9rem;
  }
  
  .tabContent {
    padding: 20px;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .serviceHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .refundHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .tabContent {
    padding: 15px;
  }
  
  .section h3 {
    font-size: 1.1rem;
  }
  
  .customerHeader h4,
  .serviceHeader h4 {
    font-size: 1.25rem;
  }
  
  .amount {
    font-size: 1.25rem;
  }
  
  .refundAmount {
    font-size: 1.1rem;
  }
  
  .transactionId,
  .externalId,
  .sessionId {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}
