import { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { formatDateTime } from '@/lib/booking-utils';
import { STATUS_DISPLAY_NAMES } from '@/lib/booking-status';
import { authenticatedFetch } from '@/lib/auth-utils';
import BookingStatusSelector from './BookingStatusSelector';
import BookingStatusHistory from './BookingStatusHistory';
import styles from '@/styles/admin/EnhancedBookingDetails.module.css';

export default function EnhancedBookingDetails({ 
  booking, 
  onClose, 
  onEdit, 
  onUpdate 
}) {
  const [activeTab, setActiveTab] = useState('details');
  const [customerHistory, setCustomerHistory] = useState([]);
  const [communications, setCommunications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const tabs = [
    { id: 'details', label: 'Booking Details', icon: '📋' },
    { id: 'customer', label: 'Customer Info', icon: '👤' },
    { id: 'history', label: 'Status History', icon: '📊' },
    { id: 'communications', label: 'Communications', icon: '💬' }
  ];

  // Fetch additional data when component mounts
  useEffect(() => {
    if (booking) {
      fetchCustomerHistory();
      fetchCommunications();
    }
  }, [booking]);

  const fetchCustomerHistory = async () => {
    if (!booking?.customer_id) return;

    try {
      setLoading(true);
      const data = await authenticatedFetch(`/api/admin/customers/${booking.customer_id}/bookings`);
      setCustomerHistory(data.bookings || []);
    } catch (error) {
      console.error('Error fetching customer history:', error);
      setError('Failed to load customer booking history');
      toast.error('Failed to load customer booking history');
    } finally {
      setLoading(false);
    }
  };

  const fetchCommunications = async () => {
    if (!booking?.id) return;

    try {
      const data = await authenticatedFetch(`/api/admin/bookings/${booking.id}/communications`);
      setCommunications(data.communications || []);
    } catch (error) {
      console.error('Error fetching communications:', error);
      // Don't show error for communications as it's not critical
      setCommunications([]);
    }
  };

  const handleStatusChange = async (newStatus) => {
    setLoading(true);
    setError(null);

    try {
      const updatedBooking = await authenticatedFetch(`/api/admin/bookings/${booking.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...booking,
          status: newStatus,
        }),
      });

      toast.success(`Booking status updated to ${STATUS_DISPLAY_NAMES[newStatus]}`);

      if (onUpdate) {
        onUpdate(updatedBooking);
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
      setError(error.message);
      toast.error('Failed to update booking status');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAction = async (action) => {
    setLoading(true);
    
    try {
      switch (action) {
        case 'send_reminder':
          await authenticatedFetch('/api/admin/bookings/bulk', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'send_reminder',
              bookingIds: [booking.id]
            })
          });
          toast.success('Reminder sent successfully');
          break;
          
        case 'call_customer':
          if (booking.customerPhone) {
            window.open(`tel:${booking.customerPhone}`);
          } else {
            toast.warn('No phone number available for this customer');
          }
          break;
          
        case 'email_customer':
          if (booking.customerEmail) {
            window.open(`mailto:${booking.customerEmail}`);
          } else {
            toast.warn('No email address available for this customer');
          }
          break;
          
        case 'reschedule':
          onEdit();
          break;
          
        default:
          console.warn('Unknown quick action:', action);
      }
    } catch (error) {
      console.error('Quick action error:', error);
      toast.error(`Failed to ${action.replace('_', ' ')}`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusClass = (status) => {
    const statusClasses = {
      confirmed: styles.confirmed,
      pending: styles.pending,
      canceled: styles.canceled,
      completed: styles.completed,
      in_progress: styles.inProgress,
      no_show: styles.noShow
    };
    return statusClasses[status] || '';
  };

  if (!booking) {
    return null;
  }

  return (
    <div className={styles.enhancedDetails}>
      <div className={styles.header}>
        <div className={styles.bookingTitle}>
          <h2>Booking #{booking.booking_reference || booking.id.slice(-8)}</h2>
          <div className={styles.bookingMeta}>
            <span className={`${styles.status} ${getStatusClass(booking.status)}`}>
              {STATUS_DISPLAY_NAMES[booking.status] || booking.status.toUpperCase()}
            </span>
            <span className={styles.service}>
              {booking.serviceName}
            </span>
            {booking.priority_level > 1 && (
              <span className={styles.priority}>
                High Priority
              </span>
            )}
          </div>
        </div>
        
        <div className={styles.headerActions}>
          <button onClick={onEdit} className={styles.editButton}>
            Edit Booking
          </button>
          <button onClick={onClose} className={styles.closeButton}>
            ×
          </button>
        </div>
      </div>

      {/* Quick Actions Bar */}
      <div className={styles.quickActions}>
        <button 
          className={styles.quickAction}
          onClick={() => handleQuickAction('send_reminder')}
          disabled={loading}
        >
          📧 Send Reminder
        </button>
        <button 
          className={styles.quickAction}
          onClick={() => handleQuickAction('call_customer')}
          disabled={!booking.customerPhone}
        >
          📞 Call Customer
        </button>
        <button 
          className={styles.quickAction}
          onClick={() => handleQuickAction('email_customer')}
          disabled={!booking.customerEmail}
        >
          ✉️ Email Customer
        </button>
        <button 
          className={styles.quickAction}
          onClick={() => handleQuickAction('reschedule')}
          disabled={loading}
        >
          📅 Reschedule
        </button>
      </div>

      {/* Tab Navigation */}
      <div className={styles.tabNavigation}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`${styles.tab} ${activeTab === tab.id ? styles.active : ''}`}
          >
            <span className={styles.tabIcon}>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className={styles.tabContent}>
        {activeTab === 'details' && (
          <BookingDetailsTab booking={booking} onStatusChange={handleStatusChange} loading={loading} />
        )}
        {activeTab === 'customer' && (
          <CustomerInfoTab
            booking={booking}
            customerHistory={customerHistory}
            getStatusClass={getStatusClass}
          />
        )}
        {activeTab === 'history' && (
          <BookingStatusHistory bookingId={booking.id} />
        )}
        {activeTab === 'communications' && (
          <CommunicationsTab 
            bookingId={booking.id}
            communications={communications}
            onRefresh={fetchCommunications}
          />
        )}
      </div>

      {error && (
        <div className={styles.errorMessage}>
          {error}
        </div>
      )}
    </div>
  );
}

// Booking Details Tab Component
function BookingDetailsTab({ booking, onStatusChange, loading }) {
  return (
    <div className={styles.detailsTab}>
      <div className={styles.infoGrid}>
        <div className={styles.infoSection}>
          <h3>Booking Information</h3>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>Start:</span>
            <span className={styles.infoValue}>{formatDateTime(booking.start_time)}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>End:</span>
            <span className={styles.infoValue}>{formatDateTime(booking.end_time)}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>Duration:</span>
            <span className={styles.infoValue}>{booking.duration || 60} minutes</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>Location:</span>
            <span className={styles.infoValue}>{booking.location || 'N/A'}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>Reference:</span>
            <span className={styles.infoValue}>{booking.booking_reference || 'N/A'}</span>
          </div>
        </div>

        <div className={styles.infoSection}>
          <h3>Service Details</h3>
          <div className={styles.serviceInfo}>
            <div
              className={styles.serviceColor}
              style={{ backgroundColor: booking.serviceColor }}
            ></div>
            <div>
              <div className={styles.serviceName}>{booking.serviceName}</div>
              <div className={styles.servicePrice}>
                ${booking.servicePrice || 0}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.infoSection}>
          <h3>Revenue Information</h3>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>Estimated:</span>
            <span className={styles.infoValue}>
              ${booking.estimated_revenue || booking.servicePrice || 0}
            </span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>Actual:</span>
            <span className={styles.infoValue}>
              ${booking.actual_revenue || 'Not recorded'}
            </span>
          </div>
        </div>
      </div>

      <div className={styles.statusManagement}>
        <h3>Status Management</h3>
        <BookingStatusSelector
          currentStatus={booking.status}
          onStatusChange={onStatusChange}
          disabled={loading}
        />
      </div>

      {booking.notes && (
        <div className={styles.notesSection}>
          <h3>Notes</h3>
          <p className={styles.notesContent}>{booking.notes}</p>
        </div>
      )}

      {booking.internal_notes && (
        <div className={styles.notesSection}>
          <h3>Internal Notes</h3>
          <p className={styles.notesContent}>{booking.internal_notes}</p>
        </div>
      )}
    </div>
  );
}

// Customer Info Tab Component
function CustomerInfoTab({ booking, customerHistory, getStatusClass }) {
  const [customerDetails, setCustomerDetails] = useState(null);
  const [loadingCustomer, setLoadingCustomer] = useState(false);

  // Fetch detailed customer information
  useEffect(() => {
    const fetchCustomerDetails = async () => {
      if (!booking?.customer_id) return;

      try {
        setLoadingCustomer(true);
        const data = await authenticatedFetch(`/api/admin/customers/${booking.customer_id}`);
        setCustomerDetails(data.customer);
      } catch (error) {
        console.error('Error fetching customer details:', error);
        // Use basic info from booking if detailed fetch fails
        setCustomerDetails({
          id: booking.customer_id,
          name: booking.customerName,
          email: booking.customerEmail,
          phone: booking.customerPhone
        });
      } finally {
        setLoadingCustomer(false);
      }
    };

    fetchCustomerDetails();
  }, [booking?.customer_id]);

  const calculateCustomerStats = () => {
    if (!customerHistory.length) return null;

    const totalRevenue = customerHistory.reduce((sum, booking) => sum + (booking.revenue || 0), 0);
    const completedBookings = customerHistory.filter(b => b.status === 'completed').length;
    const canceledBookings = customerHistory.filter(b => b.status === 'canceled').length;

    return {
      totalBookings: customerHistory.length,
      completedBookings,
      canceledBookings,
      totalRevenue,
      averageRevenue: completedBookings > 0 ? totalRevenue / completedBookings : 0,
      lastBooking: customerHistory[0]?.start_time,
      firstBooking: customerHistory[customerHistory.length - 1]?.start_time
    };
  };

  const stats = calculateCustomerStats();

  return (
    <div className={styles.customerTab}>
      <div className={styles.customerInfo}>
        <div className={styles.customerHeader}>
          <h3>Customer Details</h3>
          {booking.customer_id && (
            <Link
              href={`/admin/customers/${booking.customer_id}`}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.viewCustomerLink}
            >
              View Full Profile →
            </Link>
          )}
        </div>

        {loadingCustomer ? (
          <div className={styles.loadingCustomer}>Loading customer details...</div>
        ) : (
          <div className={styles.customerDetails}>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Name:</span>
              <span className={styles.infoValue}>
                {customerDetails?.name || booking.customerName || 'N/A'}
              </span>
            </div>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Email:</span>
              <span className={styles.infoValue}>
                {customerDetails?.email || booking.customerEmail ? (
                  <a href={`mailto:${customerDetails?.email || booking.customerEmail}`}>
                    {customerDetails?.email || booking.customerEmail}
                  </a>
                ) : 'N/A'}
              </span>
            </div>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Phone:</span>
              <span className={styles.infoValue}>
                {customerDetails?.phone || booking.customerPhone ? (
                  <a href={`tel:${customerDetails?.phone || booking.customerPhone}`}>
                    {customerDetails?.phone || booking.customerPhone}
                  </a>
                ) : 'N/A'}
              </span>
            </div>

            {customerDetails?.address && (
              <div className={styles.infoRow}>
                <span className={styles.infoLabel}>Address:</span>
                <span className={styles.infoValue}>
                  {customerDetails.address}
                  {customerDetails.city && `, ${customerDetails.city}`}
                  {customerDetails.state && `, ${customerDetails.state}`}
                  {customerDetails.postal_code && ` ${customerDetails.postal_code}`}
                </span>
              </div>
            )}

            {customerDetails?.customer_tier && (
              <div className={styles.infoRow}>
                <span className={styles.infoLabel}>Tier:</span>
                <span className={`${styles.infoValue} ${styles.customerTier} ${styles[customerDetails.customer_tier]}`}>
                  {customerDetails.customer_tier.charAt(0).toUpperCase() + customerDetails.customer_tier.slice(1)}
                </span>
              </div>
            )}

            {customerDetails?.customer_status && (
              <div className={styles.infoRow}>
                <span className={styles.infoLabel}>Status:</span>
                <span className={`${styles.infoValue} ${styles.customerStatus} ${styles[customerDetails.customer_status]}`}>
                  {customerDetails.customer_status.charAt(0).toUpperCase() + customerDetails.customer_status.slice(1)}
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {stats && (
        <div className={styles.customerStats}>
          <h3>Customer Statistics</h3>
          <div className={styles.statsGrid}>
            <div className={styles.statItem}>
              <span className={styles.statValue}>{stats.totalBookings}</span>
              <span className={styles.statLabel}>Total Bookings</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statValue}>{stats.completedBookings}</span>
              <span className={styles.statLabel}>Completed</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statValue}>${stats.totalRevenue.toFixed(2)}</span>
              <span className={styles.statLabel}>Total Revenue</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statValue}>${stats.averageRevenue.toFixed(2)}</span>
              <span className={styles.statLabel}>Avg Revenue</span>
            </div>
          </div>
        </div>
      )}

      <div className={styles.customerHistory}>
        <h3>Recent Booking History ({customerHistory.length})</h3>
        <div className={styles.historyList}>
          {customerHistory.slice(0, 5).map(historyBooking => (
            <div key={historyBooking.id} className={styles.historyItem}>
              <div className={styles.historyDate}>
                {formatDateTime(historyBooking.start_time)}
              </div>
              <div className={styles.historyService}>
                {historyBooking.serviceName}
              </div>
              <div className={styles.historyRevenue}>
                ${historyBooking.revenue || 0}
              </div>
              <div className={`${styles.historyStatus} ${getStatusClass(historyBooking.status)}`}>
                {STATUS_DISPLAY_NAMES[historyBooking.status]}
              </div>
            </div>
          ))}
          {customerHistory.length > 5 && (
            <div className={styles.moreHistory}>
              <Link
                href={`/admin/customers/${booking.customer_id}`}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.viewAllLink}
              >
                View all {customerHistory.length} bookings →
              </Link>
            </div>
          )}
          {customerHistory.length === 0 && (
            <div className={styles.noHistory}>
              No booking history available for this customer.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Communications Tab Component
function CommunicationsTab({ bookingId, communications, onRefresh }) {
  return (
    <div className={styles.communicationsTab}>
      <div className={styles.communicationsHeader}>
        <h3>Communications History</h3>
        <button onClick={onRefresh} className={styles.refreshButton}>
          🔄 Refresh
        </button>
      </div>
      
      <div className={styles.communicationsList}>
        {communications.length === 0 ? (
          <div className={styles.noCommunications}>
            No communications recorded for this booking.
          </div>
        ) : (
          communications.map(comm => (
            <div key={comm.id} className={styles.communicationItem}>
              <div className={styles.commHeader}>
                <span className={styles.commType}>{comm.communication_type}</span>
                <span className={styles.commDirection}>{comm.direction}</span>
                <span className={styles.commDate}>
                  {formatDateTime(comm.created_at)}
                </span>
              </div>
              {comm.subject && (
                <div className={styles.commSubject}>{comm.subject}</div>
              )}
              <div className={styles.commContent}>{comm.content}</div>
              <div className={styles.commStatus}>
                Status: {comm.status}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
