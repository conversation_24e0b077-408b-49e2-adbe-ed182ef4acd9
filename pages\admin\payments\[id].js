import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import PaymentDetails from '@/components/admin/PaymentDetails';
import PaymentEditForm from '@/components/admin/PaymentEditForm';
import Modal from '@/components/admin/Modal';
import { authenticatedFetch } from '@/lib/auth-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/PaymentDetailPage.module.css';

/**
 * Individual Payment Detail Page
 *
 * Displays detailed information about a specific payment transaction
 * Allows viewing transaction details, customer information, and refund history
 */
export default function PaymentDetailPage() {
  const router = useRouter();
  const { id: paymentId } = router.query;

  const [payment, setPayment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [updating, setUpdating] = useState(false);

  // Fetch payment data when component mounts or ID changes
  useEffect(() => {
    if (paymentId) {
      fetchPayment();
    }
  }, [paymentId]);

  const fetchPayment = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching payment:', paymentId);

      const data = await authenticatedFetch(`/api/admin/payments/${paymentId}`);

      if (data.payment) {
        setPayment(data.payment);
        console.log('Payment loaded:', data.payment);
      } else {
        throw new Error('Payment data not found in response');
      }
    } catch (error) {
      console.error('Error fetching payment:', error);
      setError(error.message || 'Failed to load payment');

      // If payment not found, redirect to payments list
      if (error.message?.includes('not found') || error.status === 404) {
        toast.error('Payment not found');
        router.push('/admin/payments');
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentUpdate = async (updatedPayment) => {
    try {
      setUpdating(true);

      // Update local state
      setPayment(updatedPayment);
      setShowEditModal(false);

      // Refresh payment data to ensure consistency
      await fetchPayment();
    } catch (error) {
      console.error('Error updating payment:', error);
      toast.error('Failed to update payment');
    } finally {
      setUpdating(false);
    }
  };

  const handleEdit = () => {
    setShowEditModal(true);
  };

  const handleCloseEdit = () => {
    setShowEditModal(false);
  };

  const handleBackToList = () => {
    router.push('/admin/payments');
  };

  // Loading state
  if (loading) {
    return (
      <AdminLayout>
        <ProtectedRoute>
          <div className={styles.loadingContainer}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading payment details...</p>
          </div>
        </ProtectedRoute>
      </AdminLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <AdminLayout>
        <ProtectedRoute>
          <div className={styles.errorContainer}>
            <div className={styles.errorIcon}>⚠️</div>
            <h2>Error Loading Payment</h2>
            <p>{error}</p>
            <div className={styles.errorActions}>
              <button
                onClick={fetchPayment}
                className={styles.retryButton}
              >
                Try Again
              </button>
              <button
                onClick={handleBackToList}
                className={styles.backButton}
              >
                Back to Payments
              </button>
            </div>
          </div>
        </ProtectedRoute>
      </AdminLayout>
    );
  }

  // No payment found
  if (!payment) {
    return (
      <AdminLayout>
        <ProtectedRoute>
          <div className={styles.notFoundContainer}>
            <div className={styles.notFoundIcon}>💳</div>
            <h2>Payment Not Found</h2>
            <p>The payment you're looking for doesn't exist or has been deleted.</p>
            <button
              onClick={handleBackToList}
              className={styles.backButton}
            >
              Back to Payments
            </button>
          </div>
        </ProtectedRoute>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <ProtectedRoute>
        <div className={styles.paymentDetailPage}>
          {/* Page Header */}
          <div className={styles.pageHeader}>
            <div className={styles.headerLeft}>
              <button
                onClick={handleBackToList}
                className={styles.backButton}
              >
                ← Back to Payments
              </button>
              <div className={styles.pageTitle}>
                <h1>Payment Details</h1>
                <p className={styles.paymentId}>
                  ID: {payment.id.slice(-8)}
                </p>
              </div>
            </div>

            <div className={styles.headerActions}>
              <button
                onClick={handleEdit}
                className={styles.editButton}
                disabled={updating}
              >
                {updating ? 'Updating...' : 'Edit Payment'}
              </button>
            </div>
          </div>

          {/* Payment Details */}
          <div className={styles.detailsContainer}>
            <PaymentDetails
              payment={payment}
              onEdit={handleEdit}
              onUpdate={handlePaymentUpdate}
              onClose={handleBackToList}
            />
          </div>

          {/* Edit Modal */}
          {showEditModal && (
            <Modal
              isOpen={showEditModal}
              onClose={handleCloseEdit}
              title="Edit Payment"
              size="large"
            >
              <PaymentEditForm
                payment={payment}
                onSave={handlePaymentUpdate}
                onCancel={handleCloseEdit}
              />
            </Modal>
          )}
        </div>
      </ProtectedRoute>
    </AdminLayout>
  );
}

// Add authentication requirement
PaymentDetailPage.requireAuth = true;
