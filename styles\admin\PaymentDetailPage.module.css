.paymentDetailPage {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.headerLeft {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
}

.backButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.pageTitle h1 {
  margin: 0 0 4px 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
}

.paymentId {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
}

.headerActions {
  display: flex;
  gap: 15px;
}

.editButton {
  padding: 12px 24px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.editButton:hover {
  background: #5a7cfa;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(110, 142, 251, 0.3);
}

.editButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.detailsContainer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Edit Modal Styles */
.editModalContent {
  padding: 20px;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancelButton {
  padding: 10px 20px;
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContainer p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Error States */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 40px;
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.errorContainer h2 {
  margin: 0 0 12px 0;
  font-size: 1.5rem;
  color: #d32f2f;
}

.errorContainer p {
  margin: 0 0 30px 0;
  color: #666;
  font-size: 1rem;
  max-width: 500px;
  line-height: 1.5;
}

.errorActions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.retryButton {
  padding: 12px 24px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.retryButton:hover {
  background: #5a7cfa;
  transform: translateY(-1px);
}

/* Not Found States */
.notFoundContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 40px;
}

.notFoundIcon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.6;
}

.notFoundContainer h2 {
  margin: 0 0 12px 0;
  font-size: 1.5rem;
  color: #333;
}

.notFoundContainer p {
  margin: 0 0 30px 0;
  color: #666;
  font-size: 1rem;
  max-width: 500px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .paymentDetailPage {
    padding: 15px;
  }
  
  .pageHeader {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .headerLeft {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .backButton {
    align-self: flex-start;
  }
  
  .pageTitle h1 {
    font-size: 1.5rem;
  }
  
  .headerActions {
    justify-content: center;
  }
  
  .editButton {
    width: 100%;
  }
  
  .errorActions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .retryButton,
  .backButton {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .paymentDetailPage {
    padding: 10px;
  }
  
  .pageTitle h1 {
    font-size: 1.25rem;
  }
  
  .paymentId {
    font-size: 0.8rem;
  }
  
  .editButton {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .errorContainer,
  .notFoundContainer {
    padding: 20px;
  }
  
  .errorIcon,
  .notFoundIcon {
    font-size: 3rem;
  }
  
  .errorContainer h2,
  .notFoundContainer h2 {
    font-size: 1.25rem;
  }
}
