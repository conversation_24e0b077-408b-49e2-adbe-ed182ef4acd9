import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { safeRender } from '@/lib/safe-render-utils';
import RefundModal from './RefundModal';
import { useAuth } from '@/contexts/AuthContext';
import styles from '@/styles/admin/PaymentList.module.css';

export default function PaymentList({ refreshData }) {
  const router = useRouter();
  const { role } = useAuth();
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPayments, setTotalPayments] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    payment_method: '',
    date_from: '',
    date_to: '',
  });

  // Refund modal state
  const [refundModalOpen, setRefundModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Reset page when search or filters change
  useEffect(() => {
    setPage(1);
  }, [debouncedSearch, filters]);

  // Fetch payments when search, sort, filters, or page changes
  const fetchPayments = useCallback(async () => {
    // Create an AbortController to cancel requests if component unmounts
    const controller = new AbortController();
    const signal = controller.signal;

    // Create a timeout ID for cleanup
    let timeoutId = null;

    setLoading(true);
    setError(null);

    try {
      const offset = (page - 1) * limit;
      const queryParams = new URLSearchParams({
        limit,
        offset,
        sort_by: sortBy,
        sort_order: sortOrder,
      });

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch);
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      // Create a timeout promise to prevent hanging requests
      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error('Request timeout after 15 seconds'));
        }, 15000); // 15 second timeout
      });

      // Get auth token from standardized location
      let authToken = null;
      if (typeof window !== 'undefined' && window.sessionStorage) {
        try {
          const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
          if (cachedToken) {
            const tokenData = JSON.parse(cachedToken);
            if (tokenData && tokenData.token) {
              authToken = tokenData.token;
            }
          }
        } catch (tokenError) {
          console.error('Error getting auth token:', tokenError);
        }
      }

      // Use direct fetch with proper auth headers
      const fetchPromise = fetch(`/api/admin/payments?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add auth token if available
          ...(authToken ? {
            'Authorization': `Bearer ${authToken}`,
            'X-Auth-Token': authToken
          } : {})
        },
        credentials: 'include', // Important for cookies
        signal
      }).then(response => {
        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
        return response.json();
      });

      // Race the fetch against the timeout
      const data = await Promise.race([fetchPromise, timeoutPromise]);

      // Clear the timeout since the request completed
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      setPayments(data.payments || []);
      setTotalPayments(data.total || 0);
      setTotalPages(Math.ceil((data.total || 0) / limit));
    } catch (error) {
      // Clear the timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      console.error('Error fetching payments:', error);
      setError(error.message || 'Failed to load payments');
    } finally {
      setLoading(false);
    }

    // Return cleanup function
    return () => {
      // Abort the fetch if component unmounts
      if (!signal.aborted) {
        controller.abort();
      }

      // Clear any remaining timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [page, limit, sortBy, sortOrder, debouncedSearch, filters]);

  useEffect(() => {
    fetchPayments();
  }, [fetchPayments]);

  // Handle sort column click
  const handleSort = (column) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Default to descending order for new column
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null;

    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? ' ↑' : ' ↓'}
      </span>
    );
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Format currency for display
  const formatCurrency = (amount) => {
    if (amount == null) return 'N/A';
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
    }).format(amount);
  };

  // Get status class for styling
  const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'succeeded':
        return styles.statusCompleted;
      case 'pending':
      case 'processing':
        return styles.statusPending;
      case 'failed':
      case 'canceled':
        return styles.statusFailed;
      case 'refunded':
        return styles.statusRefunded;
      case 'partially_refunded':
        return styles.statusPartiallyRefunded;
      default:
        return '';
    }
  };

  // Check if user can process refunds (Admin or DEV only)
  const canProcessRefunds = () => {
    return ['admin', 'dev'].includes(role);
  };

  // Handle refund button click
  const handleRefundClick = (payment) => {
    setSelectedPayment(payment);
    setRefundModalOpen(true);
  };

  // Handle refund success
  const handleRefundSuccess = (refund) => {
    console.log('Refund processed successfully:', refund);
    // Refresh the payments list
    fetchPayments();
    setRefundModalOpen(false);
    setSelectedPayment(null);
  };

  // Check if payment can be refunded
  const canRefundPayment = (payment) => {
    return payment.payment_status === 'completed' &&
           !['refunded'].includes(payment.payment_status);
  };

  return (
    <div className={styles.paymentListContainer}>
      <div className={styles.filters}>
        <div className={styles.searchBox}>
          <input
            type="text"
            placeholder="Search payments..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filterControls}>
          <div className={styles.filterItem}>
            <label>Status:</label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              <option value="">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
            </select>
          </div>

          <div className={styles.filterItem}>
            <label>Payment Method:</label>
            <select
              name="payment_method"
              value={filters.payment_method}
              onChange={handleFilterChange}
            >
              <option value="">All Methods</option>
              <option value="square">Square</option>
              <option value="paypal">PayPal</option>
              <option value="bank_transfer">Bank Transfer</option>
              <option value="cash">Cash</option>
            </select>
          </div>

          <div className={styles.filterItem}>
            <label>From:</label>
            <input
              type="date"
              name="date_from"
              value={filters.date_from}
              onChange={handleFilterChange}
            />
          </div>

          <div className={styles.filterItem}>
            <label>To:</label>
            <input
              type="date"
              name="date_to"
              value={filters.date_to}
              onChange={handleFilterChange}
            />
          </div>
        </div>
      </div>

      {loading && (
        <div className={styles.loadingSpinner}>Loading payments...</div>
      )}

      {error && (
        <div className={styles.errorMessage}>
          Error: {error}
          <button onClick={fetchPayments}>Try Again</button>
        </div>
      )}

      {!loading && !error && payments.length === 0 && (
        <div className={styles.noResults}>
          No payments found. Try adjusting your search or filters.
        </div>
      )}

      {!loading && !error && payments.length > 0 && (
        <>
          <div className={styles.tableContainer}>
            <table className={styles.paymentTable}>
              <thead>
                <tr>
                  <th onClick={() => handleSort('id')}>
                    ID {renderSortIndicator('id')}
                  </th>
                  <th onClick={() => handleSort('customer_name')}>
                    Customer {renderSortIndicator('customer_name')}
                  </th>
                  <th onClick={() => handleSort('amount')}>
                    Amount {renderSortIndicator('amount')}
                  </th>
                  <th onClick={() => handleSort('payment_method')}>
                    Method {renderSortIndicator('payment_method')}
                  </th>
                  <th onClick={() => handleSort('status')}>
                    Status {renderSortIndicator('status')}
                  </th>
                  <th onClick={() => handleSort('created_at')}>
                    Date {renderSortIndicator('created_at')}
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {payments.map((payment) => {
                  try {
                    return (
                      <tr key={payment.id}>
                        <td>{safeRender(payment.id, 'N/A').substring(0, 8)}...</td>
                        <td>
                          <Link href={`/admin/customers/${payment.customer_id}`}>
                            {safeRender(payment.customer_name, 'Unknown Customer')}
                          </Link>
                        </td>
                        <td>{formatCurrency(payment.amount)}</td>
                        <td className={styles.methodCell}>
                          {safeRender(payment.payment_method)}
                        </td>
                        <td>
                          <span
                            className={`${styles.statusBadge} ${getStatusClass(
                              payment.status
                            )}`}
                          >
                            {safeRender(payment.status)}
                          </span>
                        </td>
                        <td>{formatDate(payment.created_at)}</td>
                        <td className={styles.actions}>
                          <Link href={`/admin/payments/${payment.id}`}>
                            <span className={styles.viewButton}>View</span>
                          </Link>
                          {canProcessRefunds() && canRefundPayment(payment) && (
                            <button
                              onClick={() => handleRefundClick(payment)}
                              className={styles.refundButton}
                              title="Process Refund"
                            >
                              Refund
                            </button>
                          )}
                        </td>
                      </tr>
                    );
                  } catch (error) {
                    console.error('Error rendering payment row:', error, 'Payment:', payment);
                    return (
                      <tr key={payment.id || Math.random()}>
                        <td colSpan="7" style={{ color: 'red', padding: '10px' }}>
                          Error displaying payment data. Please refresh the page.
                        </td>
                      </tr>
                    );
                  }
                })}
              </tbody>
            </table>
          </div>

          <div className={styles.pagination}>
            <button
              onClick={() => setPage(1)}
              disabled={page === 1}
              className={styles.paginationButton}
            >
              First
            </button>
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={styles.paginationButton}
            >
              Previous
            </button>
            <span className={styles.pageInfo}>
              Page {page} of {totalPages}
            </span>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className={styles.paginationButton}
            >
              Next
            </button>
            <button
              onClick={() => setPage(totalPages)}
              disabled={page === totalPages}
              className={styles.paginationButton}
            >
              Last
            </button>
          </div>
        </>
      )}

      {/* Refund Modal */}
      <RefundModal
        isOpen={refundModalOpen}
        onClose={() => {
          setRefundModalOpen(false);
          setSelectedPayment(null);
        }}
        payment={selectedPayment}
        onRefundSuccess={handleRefundSuccess}
      />
    </div>
  );
}
