import { authenticateAdminRequest } from '@/lib/admin-auth'
import { getAdminClient } from '@/lib/supabase'

/**
 * API endpoint for processing payment refunds
 * POST /api/admin/payments/[id]/refund - Process a refund for a payment
 * 
 * Security: Only Admin and DEV roles can process refunds
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  const { id: paymentId } = req.query

  console.log(`[${requestId}] Refund API called for payment: ${paymentId}`)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate and authorize request
    const { user, role, authorized, error: authError } = await authenticateAdminRequest(req)

    if (authError || !authorized) {
      console.log(`[${requestId}] Authentication failed:`, authError?.message)
      return res.status(401).json({ 
        error: 'Unauthorized',
        message: authError?.message || 'Authentication required'
      })
    }

    // Check role permissions - only Admin and DEV can process refunds
    if (!['admin', 'dev'].includes(role)) {
      console.log(`[${requestId}] Insufficient permissions. Role: ${role}`)
      return res.status(403).json({ 
        error: 'Forbidden',
        message: 'Only Admin and DEV users can process refunds'
      })
    }

    const supabaseAdmin = getAdminClient()

    // Validate request body
    const {
      refund_amount,
      refund_reason,
      refund_notes = '',
      refund_method = 'manual'
    } = req.body

    // Validate required fields
    if (!refund_amount || !refund_reason) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'refund_amount and refund_reason are required'
      })
    }

    // Validate refund amount
    const amount = parseFloat(refund_amount)
    if (isNaN(amount) || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid refund amount',
        message: 'Refund amount must be a positive number'
      })
    }

    // Validate refund reason
    const validReasons = ['customer_request', 'service_issue', 'billing_error', 'other']
    if (!validReasons.includes(refund_reason)) {
      return res.status(400).json({
        error: 'Invalid refund reason',
        message: `Refund reason must be one of: ${validReasons.join(', ')}`
      })
    }

    // Validate refund method
    const validMethods = ['cash', 'square_api', 'manual']
    if (!validMethods.includes(refund_method)) {
      return res.status(400).json({
        error: 'Invalid refund method',
        message: `Refund method must be one of: ${validMethods.join(', ')}`
      })
    }

    // Get payment details
    const { data: payment, error: paymentError } = await supabaseAdmin
      .from('payments')
      .select('*')
      .eq('id', paymentId)
      .single()

    if (paymentError || !payment) {
      console.error(`[${requestId}] Payment not found:`, paymentError)
      return res.status(404).json({
        error: 'Payment not found',
        message: 'The specified payment does not exist'
      })
    }

    // Check if payment can be refunded
    const { data: canRefund, error: validationError } = await supabaseAdmin
      .rpc('can_refund_payment', {
        payment_uuid: paymentId,
        refund_amount_param: amount
      })

    if (validationError) {
      console.error(`[${requestId}] Refund validation error:`, validationError)
      return res.status(500).json({
        error: 'Validation failed',
        message: 'Unable to validate refund request'
      })
    }

    if (!canRefund) {
      return res.status(400).json({
        error: 'Refund not allowed',
        message: 'This payment cannot be refunded. It may already be fully refunded or not in a completed state.'
      })
    }

    // Process Square refund if payment method is Square-based
    let squareRefundId = null
    let refundStatus = 'pending'

    if (refund_method === 'square_api' && payment.payment_method.includes('square')) {
      try {
        const squareResult = await processSquareRefund(payment, amount, requestId)
        squareRefundId = squareResult.refund_id
        refundStatus = squareResult.status
      } catch (squareError) {
        console.error(`[${requestId}] Square refund failed:`, squareError)
        return res.status(500).json({
          error: 'Square refund failed',
          message: squareError.message || 'Failed to process Square refund'
        })
      }
    } else if (refund_method === 'cash' || refund_method === 'manual') {
      // For cash and manual refunds, mark as completed immediately
      refundStatus = 'completed'
    }

    // Create refund record
    const { data: refund, error: refundError } = await supabaseAdmin
      .from('refunds')
      .insert([{
        payment_id: paymentId,
        refund_amount: amount,
        currency: payment.currency || 'AUD',
        refund_reason,
        refund_notes,
        refund_method,
        refund_status: refundStatus,
        square_refund_id: squareRefundId,
        processed_by: user.id,
        completed_at: refundStatus === 'completed' ? new Date().toISOString() : null
      }])
      .select()
      .single()

    if (refundError) {
      console.error(`[${requestId}] Error creating refund:`, refundError)
      return res.status(500).json({
        error: 'Failed to create refund',
        message: refundError.message
      })
    }

    console.log(`[${requestId}] Refund created successfully:`, refund.id)

    // Return success response
    return res.status(201).json({
      success: true,
      refund: {
        id: refund.id,
        payment_id: refund.payment_id,
        refund_amount: refund.refund_amount,
        currency: refund.currency,
        refund_reason: refund.refund_reason,
        refund_method: refund.refund_method,
        refund_status: refund.refund_status,
        square_refund_id: refund.square_refund_id,
        processed_at: refund.processed_at,
        completed_at: refund.completed_at
      },
      message: `Refund ${refundStatus === 'completed' ? 'completed' : 'initiated'} successfully`
    })

  } catch (error) {
    console.error(`[${requestId}] Refund API Error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process refund request'
    })
  }
}

/**
 * Process Square API refund
 */
async function processSquareRefund(payment, refundAmount, requestId) {
  const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN
  const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'
  
  if (!squareAccessToken) {
    throw new Error('Square API credentials not configured')
  }

  const squareApiUrl = squareEnvironment === 'production' 
    ? 'https://connect.squareup.com'
    : 'https://connect.squareupsandbox.com'

  const refundRequest = {
    idempotency_key: `refund_${payment.id}_${Date.now()}`,
    amount_money: {
      amount: Math.round(refundAmount * 100), // Convert to cents
      currency: payment.currency || 'AUD'
    },
    payment_id: payment.transaction_id || payment.square_payment_id
  }

  console.log(`[${requestId}] Processing Square refund:`, refundRequest)

  const response = await fetch(`${squareApiUrl}/v2/refunds`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${squareAccessToken}`,
      'Content-Type': 'application/json',
      'Square-Version': '2023-10-18'
    },
    body: JSON.stringify(refundRequest)
  })

  const result = await response.json()

  if (!response.ok) {
    console.error(`[${requestId}] Square API error:`, result)
    throw new Error(result.errors?.[0]?.detail || 'Square refund failed')
  }

  return {
    refund_id: result.refund.id,
    status: result.refund.status === 'COMPLETED' ? 'completed' : 'pending'
  }
}
