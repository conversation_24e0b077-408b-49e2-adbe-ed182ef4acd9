# Ocean Soul Sparkles Refund Processing System

## Overview

The Ocean Soul Sparkles refund processing system provides secure, role-based refund functionality for payment management. This system integrates with the existing admin interface and supports multiple refund methods including cash refunds and Square API integration.

## Features

- **Role-Based Access Control**: Only Admin and DEV users can process refunds
- **Multiple Refund Methods**: Cash, Square API, and manual processing
- **Partial and Full Refunds**: Support for any amount up to the original payment
- **Audit Trail**: Complete tracking of all refund activities
- **Real-Time Status Updates**: Automatic payment status updates based on refunds
- **Square Integration**: Automated refund processing through Square API
- **Responsive Design**: Mobile-friendly admin interface

## Security Features

### Access Control
- Only users with `admin` or `dev` roles can access refund functionality
- Row Level Security (RLS) policies enforce database-level access control
- All refund activities are logged with admin user tracking

### Validation
- Refund amounts cannot exceed remaining payment balance
- Prevents duplicate refund processing
- Validates payment status before allowing refunds
- Comprehensive input validation and sanitization

### Audit Trail
- Complete refund history with timestamps
- Admin user tracking for all refund activities
- Immutable refund records (no deletion allowed)
- Payment status change tracking

## Database Schema

### Refunds Table
```sql
CREATE TABLE public.refunds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  payment_id UUID REFERENCES public.payments(id) NOT NULL,
  refund_amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'AUD',
  refund_reason TEXT NOT NULL,
  refund_notes TEXT,
  refund_method TEXT NOT NULL,
  refund_status TEXT NOT NULL DEFAULT 'pending',
  square_refund_id TEXT,
  processed_by UUID REFERENCES auth.users(id) NOT NULL,
  processed_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Constraints
- `refund_amount > 0`
- `refund_reason IN ('customer_request', 'service_issue', 'billing_error', 'other')`
- `refund_method IN ('cash', 'square_api', 'manual')`
- `refund_status IN ('pending', 'completed', 'failed', 'cancelled')`

## API Endpoints

### Process Refund
**POST** `/api/admin/payments/[id]/refund`

Process a refund for a specific payment.

**Request Body:**
```json
{
  "refund_amount": 75.00,
  "refund_reason": "customer_request",
  "refund_notes": "Customer requested partial refund",
  "refund_method": "square_api"
}
```

**Response:**
```json
{
  "success": true,
  "refund": {
    "id": "uuid",
    "payment_id": "uuid",
    "refund_amount": 75.00,
    "refund_status": "completed",
    "processed_at": "2025-01-08T10:30:00Z"
  },
  "message": "Refund completed successfully"
}
```

### Get Refund History
**GET** `/api/admin/payments/[id]/refunds`

Retrieve all refunds for a specific payment.

**Response:**
```json
{
  "success": true,
  "payment": {
    "id": "uuid",
    "amount": 150.00,
    "currency": "AUD",
    "payment_status": "partially_refunded"
  },
  "refund_summary": {
    "total_refunded": 75.00,
    "pending_refunds": 0.00,
    "remaining_amount": 75.00,
    "can_refund": true
  },
  "refunds": [...]
}
```

## Refund Methods

### Cash Refunds
- Generates refund receipt for manual cash return
- Immediately marked as completed
- Requires admin to handle physical cash transaction

### Square API Refunds
- Automated processing through Square Refunds API
- Real-time status updates from Square webhooks
- Supports both sandbox and production environments

### Manual Processing
- For custom refund scenarios
- Requires manual completion by admin
- Flexible for special circumstances

## Payment Status Updates

The system automatically updates payment status based on refund activity:

- **completed**: No refunds processed
- **partially_refunded**: Some amount refunded, balance remaining
- **refunded**: Fully refunded

## User Interface

### Payment List Integration
- Refund button appears for eligible payments
- Role-based visibility (Admin/DEV only)
- Visual status indicators for refunded payments

### Refund Modal
- Comprehensive refund form with validation
- Payment details display
- Refund history section
- Confirmation dialogs for security

### Mobile Responsive
- Touch-friendly controls
- Viewport-constrained design
- Progressive disclosure for complex data

## Helper Functions

### Database Functions
- `get_payment_refunded_amount(payment_uuid)`: Calculate total refunded amount
- `can_refund_payment(payment_uuid, refund_amount)`: Validate refund eligibility
- `update_payment_refund_status()`: Trigger function for status updates

## Configuration

### Environment Variables
```env
# Square API Configuration
SQUARE_ACCESS_TOKEN=your_square_access_token
SQUARE_ENVIRONMENT=sandbox|production
NEXT_PUBLIC_SQUARE_LOCATION_ID=your_location_id
```

### Role Configuration
Ensure users have appropriate roles in the `user_roles` table:
- `admin`: Full refund processing access
- `dev`: Full refund processing access
- Other roles: No refund access

## Testing

### Test Payment Creation
A test payment has been created for demonstration:
- Payment ID: `d2b0927a-f7dd-49cf-96d4-7b04c17c666d`
- Amount: $150.00 AUD
- Status: completed
- Method: square_terminal

### Testing Scenarios
1. **Full Refund**: Process complete refund of $150.00
2. **Partial Refund**: Process partial refund of $75.00
3. **Multiple Refunds**: Process multiple partial refunds
4. **Square Integration**: Test with Square API refunds
5. **Access Control**: Verify role-based restrictions

## Monitoring and Maintenance

### Audit Queries
```sql
-- View all refunds processed today
SELECT * FROM public.refunds 
WHERE DATE(processed_at) = CURRENT_DATE;

-- Check payment refund status distribution
SELECT payment_status, COUNT(*) 
FROM public.payments 
GROUP BY payment_status;
```

### Performance Considerations
- Indexes on payment_id, processed_by, and status fields
- Efficient queries for refund history
- Pagination for large refund lists

## Support and Troubleshooting

### Common Issues
1. **Square API Errors**: Check API credentials and environment settings
2. **Permission Denied**: Verify user role assignments
3. **Validation Errors**: Ensure refund amount doesn't exceed payment balance

### Logging
All refund activities are logged with request IDs for troubleshooting:
```
[abc123] Refund created successfully: refund-uuid
[abc123] Square refund processed: square-refund-id
```

## Future Enhancements

- Automated refund notifications to customers
- Bulk refund processing capabilities
- Advanced reporting and analytics
- Integration with accounting systems
- Refund approval workflows for large amounts
