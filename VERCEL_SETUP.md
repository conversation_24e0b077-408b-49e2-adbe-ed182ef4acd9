# Vercel Development Deployment Setup

## 🚀 Quick Fix for 403 Forbidden Error

The 403 Forbidden error was caused by production-only configuration in `next.config.js`. This has been fixed with the latest commits.

## 📋 Vercel Environment Variables Setup

### For Development Branch Deployment:

1. **Go to your Vercel Dashboard**
2. **Select your project** (connected to your fork)
3. **Go to Settings → Environment Variables**
4. **Add these variables for Development/Preview environments:**

```bash
# Copy these exact values to Vercel Environment Variables
# Set Environment: Preview (for development branch)

NODE_ENV=development
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_DEBUG_AUTH=true
ENABLE_AUTH_BYPASS=true
NEXT_PUBLIC_ENABLE_AUTH_BYPASS=true

# Supabase (same as production)
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY

# Square Sandbox (for development)
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-zmeKEI4JNUCFsS4wkL7jjQ
NEXT_PUBLIC_SQUARE_LOCATION_ID=L2DSKTPV3D3YT
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_ENVIRONMENT=sandbox

# Email Configuration
GMAIL_SMTP_HOST=smtp.gmail.com
GMAIL_SMTP_PORT=587
GMAIL_SMTP_SECURE=false
GMAIL_SMTP_USER=<EMAIL>
GMAIL_SMTP_APP_PASSWORD=jjmfjcfqqrzgsogy
GMAIL_FROM_NAME=OceanSoulSparkles
GMAIL_FROM_EMAIL=<EMAIL>

# Google Configuration
GOOGLE_CLIENT_ID=429450887908-bdaro6svl47787ec21bi11251eoolj9n.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# OneSignal
NEXT_PUBLIC_ONESIGNAL_APP_ID=************************************
ONESIGNAL_REST_API_KEY=nivzmsejbeoiehtwsovd4sjyq

# Security (relaxed for development)
NEXT_PUBLIC_ENABLE_SECURITY_HEADERS=false
NEXT_PUBLIC_DISABLE_CONSOLE_LOGS=false
FORCE_EMAIL_IN_DEV=true
```

## 🔧 Deployment Configuration

### Branch Configuration:
- **Development Branch**: Deploy from `development` branch
- **Production Branch**: Deploy from `main` branch (when ready)

### Build Settings:
- **Framework Preset**: Next.js
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Install Command**: `npm install`

## ✅ Testing Your Deployment

After setting up the environment variables:

1. **Trigger a new deployment** by pushing to the development branch
2. **Check the deployment logs** in Vercel dashboard
3. **Visit your deployment URL** - it should now work without 403 errors
4. **Test key functionality**:
   - Homepage loads
   - Admin panel accessible
   - POS Terminal works
   - Database connections work

## 🔄 Workflow Summary

```bash
# Make changes on development branch
git add .
git commit -m "Your changes"
git push origin development  # Triggers Vercel deployment

# Test in your Vercel deployment URL
# When satisfied, merge to main for production
```

## 🛠️ Troubleshooting

If you still get 403 errors:
1. Check Vercel environment variables are set correctly
2. Verify the deployment is using the development branch
3. Check build logs for any errors
4. Ensure `NODE_ENV=development` is set in Vercel

## 📞 Next Steps

1. Set up the Vercel environment variables as listed above
2. Redeploy your development branch
3. Test the deployment
4. Report back if any issues persist
