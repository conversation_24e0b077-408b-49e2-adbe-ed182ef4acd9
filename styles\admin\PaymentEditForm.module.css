.paymentEditForm {
  background: white;
  border-radius: 8px;
  padding: 0;
  max-width: 600px;
  margin: 0 auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Groups */
.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formRow {
  display: flex;
  gap: 20px;
}

.formRow .formGroup {
  flex: 1;
}

/* Labels */
.label {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
  margin-bottom: 5px;
}

.label::after {
  content: '';
}

/* Required field indicator */
.label:has(+ .input[required])::after,
.label:has(+ .select[required])::after,
.label:has(+ .textarea[required])::after {
  content: ' *';
  color: #e74c3c;
}

/* Form Inputs */
.input,
.select,
.textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
  font-family: inherit;
}

.input:focus,
.select:focus,
.textarea:focus {
  border-color: #6e8efb;
  outline: none;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

.input:disabled,
.select:disabled,
.textarea:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Error States */
.fieldError {
  border-color: #e74c3c !important;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

.errorText {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 4px;
  display: block;
  font-weight: 500;
}

/* Help Text */
.helpText {
  color: #6c757d;
  font-size: 0.85rem;
  margin-top: 4px;
  display: block;
  line-height: 1.4;
}

/* Textarea Specific */
.textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
  font-family: inherit;
}

/* Number Input Specific */
.input[type="number"] {
  -moz-appearance: textfield;
}

.input[type="number"]::-webkit-outer-spin-button,
.input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Date Input Specific */
.input[type="datetime-local"] {
  cursor: pointer;
}

.input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
  background-color: #f8f9fa;
}

/* Form Actions */
.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

/* Buttons */
.cancelButton {
  background: #f8f9fa;
  color: #495057;
  border: 2px solid #e9ecef;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.95rem;
}

.cancelButton:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.saveButton {
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.95rem;
  min-width: 120px;
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading State */
.saveButton:disabled {
  background: #6c757d;
}

/* Select Styling */
.select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}

.select:disabled {
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .paymentEditForm {
    padding: 0;
    margin: 0;
    max-width: none;
  }

  .formRow {
    flex-direction: column;
    gap: 15px;
  }

  .formActions {
    flex-direction: column-reverse;
    gap: 10px;
  }

  .cancelButton,
  .saveButton {
    width: 100%;
    padding: 14px 20px;
    font-size: 1rem;
  }

  .input,
  .select,
  .textarea {
    padding: 14px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

@media (max-width: 480px) {
  .form {
    gap: 15px;
  }

  .formGroup {
    gap: 6px;
  }

  .label {
    font-size: 0.9rem;
  }

  .helpText {
    font-size: 0.8rem;
  }

  .errorText {
    font-size: 0.8rem;
  }
}

/* Focus Management */
.form:focus-within .formGroup:not(:focus-within) {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

/* Validation Success State */
.input:valid:not(:placeholder-shown),
.select:valid:not([value=""]),
.textarea:valid:not(:placeholder-shown) {
  border-color: #28a745;
}

.input:valid:not(:placeholder-shown):focus,
.select:valid:not([value=""]):focus,
.textarea:valid:not(:placeholder-shown):focus {
  border-color: #6e8efb;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

/* Currency Input Styling */
.input[name="amount"] {
  position: relative;
}

.input[name="amount"]::before {
  content: '$';
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
}

/* Transaction ID Styling */
.input[name="transaction_id"] {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}
