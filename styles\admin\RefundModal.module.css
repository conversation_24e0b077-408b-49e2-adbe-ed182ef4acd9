/* RefundModal.module.css */
.refundModal {
  max-height: 80vh;
  overflow-y: auto;
}

.section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Payment Info Styles */
.paymentInfo {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.infoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.infoRow:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 140px;
}

.value {
  color: #333;
  font-weight: 500;
  text-align: right;
}

/* Form Styles */
.refundForm {
  background-color: #fff;
}

.formRow {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.formGroup {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input,
.select,
.textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

/* Error Styles */
.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

/* Form Actions */
.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.cancelButton,
.submitButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.cancelButton {
  background-color: #6c757d;
  color: white;
}

.cancelButton:hover:not(:disabled) {
  background-color: #5a6268;
}

.submitButton {
  background-color: #dc3545;
  color: white;
}

.submitButton:hover:not(:disabled) {
  background-color: #c82333;
}

.cancelButton:disabled,
.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Refund History Styles */
.refundHistory {
  max-height: 300px;
  overflow-y: auto;
}

.refundItem {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.refundItem:last-child {
  margin-bottom: 0;
}

.refundHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.refundAmount {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.statusCompleted {
  background-color: #d4edda;
  color: #155724;
}

.statusPending {
  background-color: #fff3cd;
  color: #856404;
}

.statusFailed {
  background-color: #f8d7da;
  color: #721c24;
}

.statusCancelled {
  background-color: #e2e3e5;
  color: #383d41;
}

.statusDefault {
  background-color: #e9ecef;
  color: #495057;
}

.refundDetails {
  font-size: 0.9rem;
}

.refundMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.5rem;
  color: #666;
}

.refundMeta span {
  font-size: 0.85rem;
}

.refundDate {
  color: #999;
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
}

.refundNotes {
  background-color: #fff;
  padding: 0.5rem;
  border-radius: 4px;
  border-left: 3px solid #007bff;
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
}

/* No Refund Message */
.noRefundMessage {
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #666;
}

.noRefundMessage p {
  margin: 0;
  font-size: 0.9rem;
}

/* Confirmation Button Style */
.confirmRefund {
  background-color: #dc3545 !important;
}

.confirmRefund:hover {
  background-color: #c82333 !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .formRow {
    flex-direction: column;
    gap: 0.5rem;
  }

  .formActions {
    flex-direction: column;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
  }

  .refundMeta {
    flex-direction: column;
    gap: 0.25rem;
  }

  .infoRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .label {
    min-width: auto;
  }

  .value {
    text-align: left;
  }
}

/* Touch-friendly sizing for mobile admin */
@media (max-width: 428px) {
  .input,
  .select,
  .textarea {
    padding: 1rem;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .cancelButton,
  .submitButton {
    padding: 1rem;
    min-height: 44px; /* Touch target size */
  }

  .section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
  }

  .refundModal {
    max-height: 85vh;
  }
}
